import 'package:bi_permissions/permissions/permissions.dart';
import 'package:embark/location/location.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';

import '../unit_test.mocks.dart';
import '../unit_test_helpers/fakes/fake_bi_permission_request_repository.dart';
import '../unit_test_helpers/fakes/fake_permission_handler_platform.dart';

void main() {
  group('LocationRepository', () {
    late MockLocationServices mockLocationServices;
    late MockPermissionHandler mockPermissionHandler;
    late LocationRepository locationRepository;
    late FakePermissionHandlerPlatform permissionHandlerPlatform;
    late FakeBiPermissionRequestRepository permissionRequestRepository;
    late MockBiLogger logger;
    final testPosition = Position(
      latitude: 0.0,
      longitude: 0.0,
      timestamp: DateTime.now(),
      accuracy: 0.0,
      altitude: 0.0,
      altitudeAccuracy: 0.0,
      heading: 0.0,
      headingAccuracy: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
    );

    setUp(
      () {
        mockLocationServices = MockLocationServices();
        mockPermissionHandler = MockPermissionHandler();
        permissionHandlerPlatform = FakePermissionHandlerPlatform.init();
        logger = MockBiLogger();

        permissionRequestRepository = FakeBiPermissionRequestRepository(
          bluetoothApi: MockBiBluetoothApi(),
        );

        locationRepository = LocationRepository(
          locationServices: mockLocationServices,
          getLocationPermissionRepository: LocationPermissionRepository(
            mockLocationServices,
            permissionRequestRepository.supportedPermissions,
            logger,
          ),
          permissionRequestRepository: permissionRequestRepository,
          logger: logger,
        );
      },
    );
    void allPermissionGranted() {
      when(mockLocationServices.serviceEnabled())
          .thenAnswer((realInvocation) async => true);

      permissionHandlerPlatform.setStatuses({
        Permission.locationWhenInUse: PermissionStatus.granted,
        Permission.locationAlways: PermissionStatus.granted,
      });

      permissionRequestRepository.setResponse(
        BiPermissionType.locationWhenInUse,
        true,
      );
      permissionRequestRepository.setResponse(
        BiPermissionType.locationAlways,
        true,
      );
    }

    group('repo tests', () {
      setUp(() {
        debugDefaultTargetPlatformOverride = TargetPlatform.iOS;
      });
      tearDown(() {
        debugDefaultTargetPlatformOverride = null;
      });
      test('requestLocationPermission calls serviceEnabled', () async {
        allPermissionGranted();

        when(mockLocationServices.serviceEnabled())
            .thenAnswer((realInvocation) async => true);

        await locationRepository.requestLocationPermission();

        verify(mockLocationServices.serviceEnabled());
      });

      test(
          'requestLocationPermission returns disabled when service is disabled',
          () async {
        when(mockLocationServices.serviceEnabled())
            .thenAnswer((realInvocation) async => false);

        final locationPermissionStatus =
            await locationRepository.requestLocationPermission();

        expect(locationPermissionStatus, LocationPermissionStatus.disabled);
      });

      test(
          'requestLocationPermission returns denied when service is enabled but permission is denied',
          () async {
        when(mockLocationServices.serviceEnabled())
            .thenAnswer((realInvocation) async => true);

        permissionHandlerPlatform.setStatus(
          Permission.locationWhenInUse,
          PermissionStatus.denied,
        );

        final locationPermissionStatus =
            await locationRepository.requestLocationPermission();

        expect(locationPermissionStatus, LocationPermissionStatus.denied);
      });
      test('does not request always permission if tracking is disabled',
          () async {
        allPermissionGranted();

        final locationPermissionStatus =
            await locationRepository.requestLocationPermission();
        verifyNever(
          mockPermissionHandler.request(
            Permission.locationAlways,
          ),
        );
        expect(locationPermissionStatus, LocationPermissionStatus.enabled);
      });
      group('has tracking enabled', () {
        test(
            'requestLocationPermission returns enabled when enabled and requests when in use permission is granted but always permission is denied',
            () async {
          when(mockLocationServices.serviceEnabled())
              .thenAnswer((realInvocation) async => true);

          permissionHandlerPlatform.setStatus(
            Permission.locationWhenInUse,
            PermissionStatus.granted,
          );
          permissionRequestRepository.setResponse(
            BiPermissionType.locationAlways,
            false,
          );

          final locationPermissionStatus =
              await locationRepository.requestLocationPermission(
            requestAlwaysPermission: true,
          );

          expect(locationPermissionStatus, LocationPermissionStatus.denied);
        });

        test(
            'requestLocationPermission returns enabled when always permission is granted',
            () async {
          allPermissionGranted();

          final locationPermissionStatus = await locationRepository
              .requestLocationPermission(requestAlwaysPermission: true);

          expect(permissionRequestRepository.permissionsRequested, isEmpty);
          expect(
            locationPermissionStatus,
            LocationPermissionStatus.enabled,
          );
        });
      });

      test('requestDeviceLocation calls getLocation', () async {
        when(mockLocationServices.getLocation())
            .thenAnswer((realInvocation) async => testPosition);

        when(mockLocationServices.serviceEnabled())
            .thenAnswer((realInvocation) async => true);

        await locationRepository.requestDeviceLocation();

        verify(mockLocationServices.getLocation());
      });

      test('requestDeviceLocation returns null if service is disabled',
          () async {
        when(mockLocationServices.getLocation())
            .thenAnswer((realInvocation) async => testPosition);

        when(mockLocationServices.serviceEnabled())
            .thenAnswer((realInvocation) async => false);

        final position = await locationRepository.requestDeviceLocation();

        verifyNever(mockLocationServices.getLocation());
        expect(position, null);
      });

      test('requestDeviceLocation returns last known location if geolocator fails', () async {
        final locationException = Exception('location exception');
        when(mockLocationServices.getLocation())
            .thenAnswer((realInvocation) async {
              throw locationException;
              // ignore: dead_code needed for the future timeout
              return testPosition;
            });

        // create a local position
        final lastKnownLocation = Position(
          latitude: 1.0,
          longitude: 1.0,
          timestamp: DateTime.now(),
          accuracy: 1.0,
          altitude: 1.0,
          altitudeAccuracy: 1.0,
          heading: 1.0,
          headingAccuracy: 1.0,
          speed: 1.0,
          speedAccuracy: 1.0,
        );

        when(mockLocationServices.getLastKnownLocation())
            .thenAnswer((realInvocation) async => lastKnownLocation);

        when(mockLocationServices.serviceEnabled())
            .thenAnswer((realInvocation) async => true);

        final position = await locationRepository.requestDeviceLocation();

        verify(mockLocationServices.getLocation());
        verify(
          logger.error(
            error: locationException,
            message: 'Error getting location',
          ),
        );
        expect(position, lastKnownLocation);
      });

      test('getLastKnownLocation calls getLastKnownLocation', () async {
        when(mockLocationServices.getLastKnownLocation())
            .thenAnswer((realInvocation) async => testPosition);

        await locationRepository.getLastKnownLocation();

        verify(mockLocationServices.getLastKnownLocation());
      });

      test('requestDeviceLocation returns a position', () async {
        when(mockLocationServices.getLocation())
            .thenAnswer((realInvocation) async => testPosition);

        when(mockLocationServices.serviceEnabled())
            .thenAnswer((realInvocation) async => true);
        final position = await locationRepository.requestDeviceLocation();

        expect(position, testPosition);
      });
    });
  });
  group('LocationPermissionRepository', () {
    late MockLocationServices mockLocationServices;
    late MockBiSupportedPermissions mockPermissions;
    late MockBiLogger logger;
    late LocationPermissionRepository locationPermissionRepository;
    final permissionGranted = _FakeBiPermission(localGranted: true);
    final permissionDenied = _FakeBiPermission(localGranted: false);

    setUp(() {
      mockLocationServices = MockLocationServices();
      mockPermissions = MockBiSupportedPermissions();
      logger = MockBiLogger();
      locationPermissionRepository = LocationPermissionRepository(
        mockLocationServices,
        mockPermissions,
        logger,
      );
    });

    test('locationPermissionsEnabled returns true if when in use permission is granted', () async {
      when(mockPermissions.locationWhenInUse)
          .thenReturn(permissionGranted);
      when(mockPermissions.locationAlways)
          .thenReturn(permissionDenied);

      expect(await locationPermissionRepository.locationPermissionsEnabled(), true);
    });

    test('locationPermissionsEnabled returns true if when always permission is granted', () async {
      when(mockPermissions.locationWhenInUse)
          .thenReturn(permissionDenied);
      when(mockPermissions.locationAlways)
          .thenReturn(permissionGranted);

      expect(await locationPermissionRepository.locationPermissionsEnabled(), true);
    });
    test('locationPermissionsEnabled returns false if when in use permission is not granted', () async {
      when(mockPermissions.locationWhenInUse)
          .thenReturn(permissionDenied);
      when(mockPermissions.locationAlways)
          .thenReturn(permissionDenied);

      expect(await locationPermissionRepository.locationPermissionsEnabled(), false);
    });

    test('serviceEnabled returns true if service is enabled', () async {
      when(mockLocationServices.serviceEnabled())
          .thenAnswer((realInvocation) async => true);

      expect(await locationPermissionRepository.serviceEnabled(), true);
    });

    test('serviceEnabled returns false if service is disabled', () async {
      when(mockLocationServices.serviceEnabled())
          .thenAnswer((realInvocation) async => false);

      expect(await locationPermissionRepository.serviceEnabled(), false);
    });

    test('canUseLocationAlways returns true if when always permission is granted', () async {
      when(mockPermissions.locationWhenInUse)
          .thenReturn(permissionDenied);
      when(mockPermissions.locationAlways)
          .thenReturn(permissionGranted);
      when(mockLocationServices.serviceEnabled())
          .thenAnswer((realInvocation) async => true);

      expect(await locationPermissionRepository.canUseLocationAlways(), true);
    });

    test('canUseLocationAlways returns false if when always permission is not granted', () async {
      when(mockPermissions.locationWhenInUse)
          .thenReturn(permissionGranted);
      when(mockPermissions.locationAlways)
          .thenReturn(permissionDenied);
      when(mockLocationServices.serviceEnabled())
          .thenAnswer((realInvocation) async => true);

      expect(await locationPermissionRepository.canUseLocationAlways(), false);
    });

    test('canUseLocationAlways returns false if when location services are not enabled', () async {
      when(mockPermissions.locationWhenInUse)
          .thenReturn(permissionDenied);
      when(mockPermissions.locationAlways)
          .thenReturn(permissionGranted);
      when(mockLocationServices.serviceEnabled())
          .thenAnswer((realInvocation) async => false);

      expect(await locationPermissionRepository.canUseLocationAlways(), false);
    });
  });
}

class _FakeBiPermission extends Fake implements BiPermission {
  final bool localGranted;

  _FakeBiPermission({required this.localGranted}) : super();

  @override
  Future<bool> get granted => Future.value(localGranted);
}
