import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/databases/key_value/models/client/_sensor_event_info_model.dart';
import 'package:embark/repositories/_sensor_event_info_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  late MockSensorEventInfoBox box;
  late MockBiometricRepository biometricRepository;
  late MockBiLogger logger;
  late SensorEventInfoRepository sensorEventInfoRepository;

  setUp(() {
    box = MockSensorEventInfoBox();
    biometricRepository = MockBiometricRepository();
    logger = MockBiLogger();
    sensorEventInfoRepository = SensorEventInfoRepository(
      box,
      biometricRepository,
      logger,
    );
    when(box.put(any, any)).thenAnswer((realInvocation) => Future.value());
  });

  group('SensorEventInfoRepository', () {
    group('add', () {

      for(final event in SensorEventType.values) {
        final sensorEventInfo = SensorEventInfo(
          null,
          DateTime.fromMillisecondsSinceEpoch(123),
          1,
          sensorEventType: event,
        );
        final sensorEventInfoModel = SensorEventInfoModel(
          json: sensorEventInfo.toJson(),
          id: sensorEventInfo.hashCode,
        );

        test('adding sensor event info for immediate upload', () async {
          when(box.values).thenReturn([sensorEventInfoModel]);
          when(biometricRepository.postSensorsInfo(any)).thenAnswer(
                (realInvocation) => Future.value(ApiResponse.ok()),
          );
          await sensorEventInfoRepository.add(sensorEventInfo);
          verify(box.put(sensorEventInfo.hashCode, any)).called(1);
          verify(biometricRepository.postSensorsInfo([sensorEventInfo]))
              .called(1);
          verify(box.delete(sensorEventInfoModel.id)).called(1);
        });

        test('upload was not successful', () async {
          when(biometricRepository.postSensorsInfo(any)).thenAnswer(
            (realInvocation) => Future.value(ApiResponse.fromCode(httpCode: 500)),
          );
          when(box.values).thenReturn([sensorEventInfoModel]);
          await sensorEventInfoRepository.add(sensorEventInfo);
          verifyNever(box.delete(sensorEventInfoModel.id));
          verify(logger.error(message: 'Failed to upload sensor data', error: anyNamed('error'))).called(1);
        });

        test('adding sensor event info for immediate upload fails', () async {
          final exception = Exception('ha ha');
          when(biometricRepository.postSensorsInfo(any)).thenThrow(exception);
          when(box.values).thenReturn([sensorEventInfoModel]);
          await sensorEventInfoRepository.add(sensorEventInfo);
          verify(biometricRepository.postSensorsInfo([sensorEventInfo]))
              .called(1);
          verifyNever(box.delete(sensorEventInfoModel.id));

          // Error message is logged
          verify(logger.error(message: 'Failed to upload sensor data', error: exception))
              .called(1);

          // Sensor event info is stored
          final verification = verify(box.put(any, captureAny));
          expect(verification.captured.length, 1);
          expect(verification.captured[0].id, sensorEventInfo.hashCode);
          expect(verification.captured[0].json, sensorEventInfo.toJson());
        });
      }
    });
    test('deleting sensor event info', () async {
      final sensorEventInfo = SensorEventInfo(
        null,
        DateTime.fromMillisecondsSinceEpoch(123),
        1,
        sensorEventType: SensorEventType.strapTamper,
      );
      await sensorEventInfoRepository.delete(sensorEventInfo.hashCode);
      verify(box.delete(any)).called(1);
    });

    test('clearing sensor event info', () async {
      await sensorEventInfoRepository.clear();
      verify(box.clear()).called(1);
    });

    test('get stored events', () async {
      final sensorEventInfo = SensorEventInfo(
        null,
        DateTime.fromMillisecondsSinceEpoch(123),
        1,
        sensorEventType: SensorEventType.strapTamper,
      );
      final sensorEventInfoModel = SensorEventInfoModel(
        json: sensorEventInfo.toJson(),
        id: sensorEventInfo.hashCode,
      );
      when(box.values).thenReturn([sensorEventInfoModel]);
      final eventsReturned = await sensorEventInfoRepository.getSensorEvents();
      expect(eventsReturned, [sensorEventInfoModel]);
    });
  });
}
