import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:embark/starter.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:flutter/material.dart';

import '../unit_test.mocks.dart';

void main() {
  late MockAppRepository mockAppRepository;
  late MockSmartLinkAppSettingsHttpClient mockAppSettingsApi;
  late MockSharedPreferencesService mockSharedPreferencesService;
  late MockBiLogger mockLogger;
  late AppSettingsRepository appSettingsRepository;
  late BiStreamController<AuthenticationStatusBase> streamController;

  setUp(() {
    mockAppRepository = MockAppRepository();
    mockAppSettingsApi = MockSmartLinkAppSettingsHttpClient();
    mockSharedPreferencesService = MockSharedPreferencesService();
    mockLogger = MockBiLogger();
    streamController = BiStreamController<AuthenticationStatusBase>(sync: true);
    when(mockAppRepository.authenticationStatusStream)
        .thenAnswer((_) => streamController.stream);
    appSettingsRepository = AppSettingsRepository(
      mockAppSettingsApi,
      mockSharedPreferencesService,
      mockAppRepository,
      mockLogger,
    );
  });

  tearDown(() {
    appSettingsRepository.close();
    streamController.close();
    getIt.reset();
  });

  test('Language change should be submitted if pending', () async {
    when(mockSharedPreferencesService.getBool('languagePendingUpload', false))
        .thenReturn(true);
    when(mockAppRepository.getStoredLocale()).thenReturn(const Locale('en'));
    when(mockAppRepository.hasSelectedLanguage).thenReturn(true);

    streamController.add(
      AuthenticationStatusBase.authenticated(
        returnedFromBackground: false,
      ),
    );

    await pumpEventQueue();

    verify(mockAppSettingsApi.postEventsLanguage(53)).called(1);
    verify(mockSharedPreferencesService.setBool('languagePendingUpload', false))
        .called(1);
  });

  test('Language change should not be submitted if not pending', () async {
    when(mockSharedPreferencesService.getBool('languagePendingUpload', false))
        .thenReturn(false);

    streamController.add(
      AuthenticationStatusBase.authenticated(
        returnedFromBackground: false,
      ),
    );

    await pumpEventQueue();

    verifyNever(mockAppSettingsApi.postEventsLanguage(any));
  });

  test(
      'should call setTranslateMessages and storeTranslateMessages when locale is not en',
      () {
    const locale = Locale('es');

    appSettingsRepository.setAppLocale(locale);

    verify(mockAppRepository.setTranslateMessages(true)).called(1);
    verify(mockAppRepository.storeTranslateMessages(true)).called(1);
  });

  test(
      'should not call setTranslateMessages and storeTranslateMessages when locale is en',
      () {
    const locale = Locale('en'); // English locale

    appSettingsRepository.setAppLocale(locale);

    verifyNever(mockAppRepository.setTranslateMessages(true));
    verifyNever(mockAppRepository.storeTranslateMessages(true));
  });

  test('postAcceptTermsAndConditions should call api', () async {
    await appSettingsRepository.postAcceptTermsAndConditions();
    verify(mockAppSettingsApi.postAcceptTerms()).called(1);
  });
}
