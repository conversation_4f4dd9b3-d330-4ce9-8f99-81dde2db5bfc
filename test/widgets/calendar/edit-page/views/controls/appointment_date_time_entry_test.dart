import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:clock/clock.dart';
import 'package:embark/calendar/edit-page/views/controls/_appointment_date_time_entry.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:provider/provider.dart';

import '../../../../widget_test_helpers/golden_wrapper.dart';

void main() {
  const size = Size(300, 250);
  final minDate = DateTime(2021, 1, 1);
  final maxDate = DateTime(2021, 12, 31);
  Widget loadWidget(
      bool enabled,
      CalendarRuleInfo ruleInfo,
      String dateTitle,
      String timeTitle,
      void Function(DateTimePickerValue?) onValueChange,
      DateTime? maxDateTime,
      DateTime? minDateTime,
      GlobalKey<FormFieldState<DateTimePickerValue>>? fieldKey,
      bool allowNullDate,
      bool hasNullDate,
      bool verticalOrientation,
      String recurrenceSectionTitle,
      String timeSectionTitle,
      ) {
    return MaterialApp(
      localizationsDelegates: const [
        BiWidgetLocalizations.delegate,
        ...AppLocalizations.localizationsDelegates,
      ],
      home: Provider<Clock>(
        create: (BuildContext context) => Clock(),
        child: Scaffold(
          body: SizedBox(
            height: size.height,
            width: size.width,
            child: AppointmentDateTimeEntry(
              enabled: enabled,
              field: ruleInfo,
              dateTitle: dateTitle,
              timeTitle: timeTitle,
              onValueChange: onValueChange,
              maxDateTime: maxDateTime,
              minDateTime: minDateTime,
              fieldKey: fieldKey,
              l10n: AppLocalizationsEn(),
              allowNullDate: allowNullDate,
              hasNullDate: hasNullDate,
              verticalOrientation: verticalOrientation,
              recurrenceSectionTitle: recurrenceSectionTitle,
              timeSectionTitle: timeSectionTitle,
            ),
          ),
        ),
      ),
    );
  }

  group('AppointmentDateTimeEntry', () {
    testGoldens('renders correctly', (tester) async {
      final widget = loadWidget(
          true,
          CalendarRuleInfo(),
          'date',
          'time',
          (value) {},
          maxDate,
          minDate,
          null,
          true,
          true,
          true,
          'recurrence',
          'time',
      );
      await tester.pumpWidgetBuilder(
          widget,
          surfaceSize: size,
          wrapper: goldenWrapper,
      );
      await screenMatchesGolden(tester, 'appointment_date_time_entry');
    });
  });
}
