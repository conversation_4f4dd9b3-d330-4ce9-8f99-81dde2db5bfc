import 'package:audioplayers/audioplayers.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_flutter_bluetooth_platform_api/plugin/plugin.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_login_widget/login/bloc/login_bloc.dart';
import 'package:bi_flutter_login_widget/services/secure_storage_service.dart';
import 'package:bi_flutter_mdm_platform_api/platform/_bi_mdm_platform_api.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/permissions/_bi_supported_permissions.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:embark/address_change_request/cubit/cubit.dart';
import 'package:embark/api/_dio_device_type_switcher.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/calendar/tab-page/cubit/calendar_cubit.dart';
import 'package:embark/call_history/cubit/call_history_cubit.dart';
import 'package:embark/call_maker/cubit/call_maker_cubit.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/check_in/face_detector_service.dart';
import 'package:embark/contact_info_change_request/cubit/cubit.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/databases/databases.dart';
import 'package:embark/databases/key_value/models/_open_text_update.dart';
import 'package:embark/databases/key_value/models/client/_sensor_event_info_model.dart';
import 'package:embark/device_setup/cubit/_device_setup_cubit.dart';
import 'package:embark/documents/cubit/_document_cubit.dart';
import 'package:embark/documents/cubit/_documents_repository.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/internet_connection/internet_connection_listener.dart';
import 'package:embark/language/cubit/language_cubit.dart';
import 'package:embark/location/_location_services.dart';
import 'package:embark/login/cubit/login_cubit.dart';
import 'package:embark/messages/cubit/messages_cubit.dart';
import 'package:embark/navigation_observers/_arrived_on_dashboard_observer.dart';
import 'package:embark/navigation_observers/navigation_observers.dart';
import 'package:embark/notification_history/cubit/notification_history_cubit.dart';
import 'package:embark/open_text_change_request/cubit/cubit.dart';
import 'package:embark/permissions/cubit/permissions_cubit.dart';
import 'package:embark/profile/cubit/cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notifications/cubit/push_notifications_cubit.dart';
import 'package:embark/repositories/_app_version_repository.dart';
import 'package:embark/repositories/_background_isolate_starter_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/_profile_repository.dart';
import 'package:embark/repositories/_tracking/_tracking_config_repository.dart';
import 'package:embark/repositories/_voip_call_repository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/resources/cubit/resources_cubit.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/services/_wake_lock_service.dart';
import 'package:embark/services/services.dart';
import 'package:embark/settings/cubit/settings_cubit.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:embark/starter.dart';
import 'package:embark/terms_and_conditions/cubit/terms_and_conditions_cubit.dart';
import 'package:embark/video_conference/cubit/video_conference_cubit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:hive/hive.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:twilio_programmable_video/twilio_programmable_video.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';

Future<AppLocalizations> loadEnglishTranslations() =>
    AppLocalizations.delegate.load(const Locale('en', 'US'));

@GenerateNiceMocks([
  MockSpec<Box<Address>>(as: #MockAddressBox),
  MockSpec<Box<ClientSettingsDatabaseModel>>(as: #MockClientSettingsBox),
  MockSpec<CalendarRepository>(),
  MockSpec<AppSettingsRepository>(),
  MockSpec<EmbarkAuthenticationRepository>(),
  MockSpec<MessagesRepository>(),
  MockSpec<SharedPreferencesService>(),
  MockSpec<RemoteNotificationsService>(),
  MockSpec<BiLogger>(),
  MockSpec<AppRepository>(),
  MockSpec<SmartLinkAppSettingsHttpClient>(),
  MockSpec<Box<CalendarPreference>>(as: #MockCalendarPreferenceBox),
  MockSpec<EmbarkKeyValueDatabase>(),
  MockSpec<Box<PushNotificationsInfo>>(as: #MockPushNotificationsInfoBox),
  MockSpec<PushNotificationsRepository>(),
  MockSpec<Connectivity>(),
  MockSpec<DocumentsRepository>(),
  MockSpec<SettingsCubit>(),
  MockSpec<FeatureFlags>(),
  MockSpec<L10nAssetLoader>(),
  MockSpec<AssetBundle>(),
  MockSpec<BiPackageInfo>(),
  MockSpec<PlatformDispatcher>(),
  MockSpec<ProfileCubit>(),
  MockSpec<DashboardCubit>(),
  MockSpec<SelfReportCubit>(),
  MockSpec<BiometricRepository>(),
  MockSpec<SmartLinkBiometricHttpClient>(),
  MockSpec<TransportationRepository>(),
  MockSpec<GetTransportationRulesResponse>(),
  MockSpec<SmartLinkTransportationHttpClient>(),
  MockSpec<SmartLinkClientHttpClient>(),
  MockSpec<SmartLinkCommunityProviderHttpClient>(),
  MockSpec<SmartLinkCalendarHttpClient>(),
  MockSpec<SmartLinkFirmwareInfoHttpClient>(),
  MockSpec<ClientSettingsRepository>(),
  MockSpec<BiTokenManager>(),
  MockSpec<AddressRepository>(),
  MockSpec<AddressChangeRequestCubit>(),
  MockSpec<EmploymentRepository>(),
  MockSpec<ContactInfoRepository>(),
  MockSpec<ProfileRepository>(),
  MockSpec<SmartLinkContactInfoHttpClient>(),
  MockSpec<ContactInfoChangeRequestCubit>(),
  MockSpec<PersonalContactsRepository>(),
  MockSpec<OpenTextChangeRequestCubit>(),
  MockSpec<OpenTextRepository>(),
  MockSpec<SmartLinkNotificationsHttpClient>(),
  MockSpec<SmartLinkAddressHttpClient>(),
  MockSpec<DocumentLauncher>(),
  MockSpec<Box<PersonalContactRequestInfo>>(
    as: #MockPersonalContactRequestInfoBox,
  ),
  MockSpec<Box<OpenTextUpdate>>(as: #MockOpenTextUpdateBox),
  MockSpec<Box<ContactInfoUpdate>>(as: #MockContactInfoUpdateBox),
  MockSpec<Box<CommunityReferralDatabaseModel>>(as: #MockCommunityReferralBox),
  MockSpec<Box<TransportationRequestInfo>>(as: #MockTransportationRequestInfoBox),
  MockSpec<SmartLinkEntityContactHttpClient>(),
  MockSpec<UrlLauncherPlatform>(),
  MockSpec<CommunityReferralRepository>(),
  MockSpec<ClientRepository>(),
  MockSpec<HomeCubit>(),
  MockSpec<MessagesCubit>(),
  MockSpec<DocumentsCubit>(),
  MockSpec<ResourcesCubit>(),
  MockSpec<CalendarCubit>(),
  MockSpec<PinRepository>(),
  MockSpec<LocationServices>(),
  // ignore: deprecated_member_use_from_same_package
  MockSpec<PermissionHandler>(),
  MockSpec<BatteryRepository>(),
  MockSpec<InternetConnectivityRepository>(),
  MockSpec<Battery>(),
  MockSpec<LocationRepository>(),
  MockSpec<LocationPermissionRepository>(),
  MockSpec<BiSupportedPermissions>(),
  MockSpec<DeviceInfoRepository>(),
  MockSpec<DeviceInfoPlugin>(),
  MockSpec<IosDeviceInfo>(),
  MockSpec<AndroidDeviceInfo>(),
  MockSpec<IosUtsname>(),
  MockSpec<NotificationHistoryCubit>(),
  MockSpec<BiMdmPlatformApi>(),
  MockSpec<DeviceSetupRepository>(),
  MockSpec<BiAuthenticationRepository>(),
  MockSpec<LanguageCubit>(),
  MockSpec<PermissionsCubit>(),
  MockSpec<AppSettings>(),
  MockSpec<PushNotificationsCubit>(),
  MockSpec<AndroidBuildVersion>(),
  MockSpec<GetEmploymentRulesResponse>(),
  MockSpec<EmploymentRules>(),
  MockSpec<EmploymentContactInfoRules>(),
  MockSpec<SmartLinkMediaClient>(),
  MockSpec<EmploymentAddressRules>(),
  MockSpec<DioDeviceTypeSwitcher>(),
  MockSpec<EmbarkAuthenticationBloc>(),
  MockSpec<VideoConferenceRepository>(),
  MockSpec<TwilioProgrammableVideoService>(),
  MockSpec<Room>(),
  MockSpec<CameraSourceService>(),
  MockSpec<CameraCapturer>(),
  MockSpec<RemoteParticipant>(),
  MockSpec<RemoteVideoTrackSubscriptionEvent>(),
  MockSpec<RemoteVideoTrackPublication>(),
  MockSpec<LocalVideoTrackPublication>(),
  MockSpec<RemoteVideoTrack>(),
  MockSpec<ConnectOptions>(),
  MockSpec<LocalParticipant>(),
  MockSpec<LocalVideoTrack>(),
  MockSpec<VoIPCallRepository>(),
  MockSpec<CallHistoryCubit>(),
  MockSpec<AudioPlayer>(),
  MockSpec<CameraControllerFactory>(),
  MockSpec<CameraController>(),
  MockSpec<FaceDetector>(),
  MockSpec<TwilioDialer>(),
  MockSpec<TrackingConfigRepository>(),
  MockSpec<PushNotificationsTokenSerivce>(),
  MockSpec<PushNotificationListenerCubit>(),
  MockSpec<LoginCubit>(),
  MockSpec<AppVersionRepository>(),
  MockSpec<BiCdnHttpClient>(),
  MockSpec<CalendarPreferenceService>(),
  MockSpec<NavigatorObserver>(),
  MockSpec<CalendarPreference>(),
  MockSpec<ImageCompression>(),
  MockSpec<FaceDetectionUtil>(),
  MockSpec<FaceDetectionService>(),
  MockSpec<TermsAndConditionsCubit>(),
  MockSpec<HttpClientAdapter>(),
  MockSpec<CameraUtil>(),
  MockSpec<CameraImage>(),
  MockSpec<LoginBloc>(),
  MockSpec<ErrorInterceptorHandler>(),
  MockSpec<DioException>(),
  MockSpec<VideoConferenceCubit>(),
  MockSpec<FlutterLocalNotificationsPlugin>(),
  MockSpec<Starter>(),
  MockSpec<SmartBandPreferences>(),
  MockSpec<Box<EmploymentRequestInfo>>(as: #MockEmploymentRequestInfoBox),
  MockSpec<SmartLinkEmploymentHttpClient>(),
  MockSpec<CallMakerCubit>(),
  MockSpec<AuthManager>(),
  MockSpec<AuthHandler>(),
  MockSpec<WakelockService>(),
  MockSpec<ArrivedOnDashboardObserver>(),
  MockSpec<InternetConnectionListener>(),
  MockSpec<DeviceSetupCubit>(),
  MockSpec<FlutterSecureStorage>(),
  MockSpec<RequestInterceptorHandler>(),
  MockSpec<LocaleStreamRepository>(),
  MockSpec<SecureStorageService>(),
  MockSpec<RemoteAudioTrack>(),
  MockSpec<RemoteAudioTrackPublication>(),
  MockSpec<SmartLinkVoIPCallHttpClient>(),
  MockSpec<BiBluetoothApi>(),
  MockSpec<BluetoothFrontend>(),
  MockSpec<SmartBandSettingsCubit>(),
  MockSpec<SensorEventInfoRepository>(),
  MockSpec<Box<SensorEventInfoModel>>(as: #MockSensorEventInfoBox),
  MockSpec<BackgroundIsolateStarterRepository>(),
  MockSpec<BiPermissionRequestRepository>(),
  MockSpec<EmbarkPermissionPreferences>(),
  MockSpec<SharedPreferences>(),
])
void main() {}
