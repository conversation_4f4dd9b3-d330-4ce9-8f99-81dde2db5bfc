import 'package:intl/date_symbol_data_custom.dart';
import 'package:intl/date_symbols.dart';
import 'package:intl/date_time_patterns.dart';

class UrduDateTime {
  static Map<String, String> get _dateTimeFormats {
    // Start by getting the English date-time formats as a base.
    final englishDateFormats =
        Map<String, String>.of(dateTimePatternMap()['en']!);

    // Ensure 24-hour format for Urdu.
    englishDateFormats['j'] = 'HH'; // Ensure 24-hour format for 'j'.

    return englishDateFormats;
  }

  static DateSymbols get _dateSymbols => DateSymbols(
        NAME: 'ur',
        ERAS: const [
          'ق م',
          'عیسوی',
        ],
        ERANAMES: const [
          'قبل مسیح',
          'عیسوی',
        ],
        NARROWMONTHS: const [
          'ج',
          'ف',
          'م',
          'ا',
          'م',
          'ج',
          'ج',
          'ا',
          'س',
          'ا',
          'ن',
          'د',
        ],
        STANDALONENARROWMONTHS: const [
          'ج',
          'ف',
          'م',
          'ا',
          'م',
          'ج',
          'ج',
          'ا',
          'س',
          'ا',
          'ن',
          'د',
        ],
        MONTHS: const [
          'جنوری',
          'فروری',
          'مارچ',
          'اپریل',
          'مئی',
          'جون',
          'جولائی',
          'اگست',
          'ستمبر',
          'اکتوبر',
          'نومبر',
          'دسمبر',
        ],
        STANDALONEMONTHS: const [
          'جنوری',
          'فروری',
          'مارچ',
          'اپریل',
          'مئی',
          'جون',
          'جولائی',
          'اگست',
          'ستمبر',
          'اکتوبر',
          'نومبر',
          'دسمبر',
        ],
        SHORTMONTHS: const [
          'جن',
          'فر',
          'مار',
          'اپر',
          'مئی',
          'جون',
          'جول',
          'اگ',
          'ست',
          'اکت',
          'نو',
          'دس',
        ],
        STANDALONESHORTMONTHS: const [
          'جن',
          'فر',
          'مار',
          'اپر',
          'مئی',
          'جون',
          'جول',
          'اگ',
          'ست',
          'اکت',
          'نو',
          'دس',
        ],
        WEEKDAYS: const [
          'اتوار',
          'پیر',
          'منگل',
          'بدھ',
          'جمعرات',
          'جمعہ',
          'ہفتہ',
        ],
        STANDALONEWEEKDAYS: const [
          'اتوار',
          'پیر',
          'منگل',
          'بدھ',
          'جمعرات',
          'جمعہ',
          'ہفتہ',
        ],
        SHORTWEEKDAYS: const [
          'اتوار',
          'پیر',
          'منگل',
          'بدھ',
          'جمعرات',
          'جمعہ',
          'ہفتہ',
        ],
        STANDALONESHORTWEEKDAYS: const [
          'اتوار',
          'پیر',
          'منگل',
          'بدھ',
          'جمعرات',
          'جمعہ',
          'ہفتہ',
        ],
        NARROWWEEKDAYS: const [
          'ا',
          'پ',
          'م',
          'ب',
          'ج',
          'ج',
          'ہ',
        ],
        STANDALONENARROWWEEKDAYS: const [
          'ا',
          'پ',
          'م',
          'ب',
          'ج',
          'ج',
          'ہ',
        ],
        SHORTQUARTERS: const [
          'پہلی سہ ماہی',
          'دوسری سہ ماہی',
          'تیسری سہ ماہی',
          'چوتھی سہ ماہی',
        ],
        QUARTERS: const [
          'پہلی سہ ماہی',
          'دوسری سہ ماہی',
          'تیسری سہ ماہی',
          'چوتھی سہ ماہی',
        ],
        AMPMS: const ['صبح', 'شام'],
        DATEFORMATS: const [
          'EEEE، d MMMM، y',
          'd MMMM، y',
          'd MMM، y',
          'd/M/yy',
        ],
        TIMEFORMATS: const [
          'h:mm:ss a zzzz',
          'h:mm:ss a z',
          'h:mm:ss a',
          'h:mm a',
        ],
        FIRSTDAYOFWEEK: 6,
        WEEKENDRANGE: const [5, 6],
        FIRSTWEEKCUTOFFDAY: 5,
        DATETIMEFORMATS: const [
          '{1} {0}',
          '{1} {0}',
          '{1} {0}',
          '{1} {0}',
        ],
      );

  static void initialize() {
    initializeDateFormattingCustom(
      locale: 'ur',
      patterns: UrduDateTime._dateTimeFormats,
      symbols: UrduDateTime._dateSymbols,
    );
  }
}
