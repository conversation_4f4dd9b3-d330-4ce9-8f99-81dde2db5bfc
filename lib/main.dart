import 'dart:async';
import 'dart:io' as io;

import 'package:bi_embark_l10n/l10n/locale_setup.dart';
import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:bi_flutter_notifications/plugin/_bi_flutter_notifications.dart';
import 'package:clock/clock.dart';
import 'package:embark/_smartlink_background_service_entrypoint.dart';
import 'package:embark/app.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/databases/key_value/_embark_key_value_database.dart';
import 'package:embark/databases/key_value/models/_push_notifications_info.dart';
import 'package:embark/repositories/smart_band/_smart_band_ui_repository.dart';
import 'package:embark/services/background_jobs/work_manager/_callback_dispatcher.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:workmanager/workmanager.dart';

bool backgroundDependenciesInitialized = false;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeDateFormatting();
  CustomLocaleSetup.initializeDateTimes();

  // init firebase nice and early.
  final firebaseOptions = await AppSettings.loadFirebaseOptions();
  await BiFirebase.init(firebaseOptions);

  getIt.registerSingleton<Starter>(
    await StarterImplementation.standard(),
  );
  final starter = getIt.get<Starter>();

  await starter.initializeDependencyInjection(isForeground: true);
  debugPrint('smartlink process Id: ${io.pid}');
  // TODO: Placed outside of Starter right now since Aaron has some pending
  // refactor work going on in there to split out UI vs. background registrations.
  getIt.registerLazySingleton(
    () => SmartBandUiRepository(
      sharedPreferencesService: getIt.get(),
      bluetoothApi: getIt.get(),
      frontend: getIt.get(),
      sensorEventInfoRepository: getIt.get(),
      backgroundIsolateStarterRepository: getIt.get(),
      permissionRequestRepository: getIt.get(),
      smartLinkFirmwareInfoHttpClient: getIt.get(),
    ),
  );
  final remoteNotificationsService = getIt.get<RemoteNotificationsService>(
    instanceName: 'RemoteNotificationsService',
  );
  await remoteNotificationsService.initializeLocalNotifications();

  await BiBackgroundService.configure(
    backgroundService: getIt.get(),
    localNotifications: getIt.get(),
    channel: const AndroidNotificationChannel(
      'bi_background_service',
      'BI SmartLINK',
      importance: Importance.min,
    ),
    foregroundServiceTypes: [
      AndroidForegroundType.connectedDevice,
      AndroidForegroundType.location,
    ],
    entrypoint: smartlinkBackgroundServiceEntrypoint,
  );

  if (defaultTargetPlatform == TargetPlatform.android) {
    await getIt.get<BiFlutterNotifications>().onBackgroundMessage(
          firebaseMessagingBackgroundHandler,
        );
  }

  final app = await buildApp(
    GetIt.instance.get<AppSettings>(),
    GetIt.instance.get<EmbarkKeyValueDatabase>(),
    GetIt.instance.get<Clock>(),
    GetIt.instance.get<BiLogger>(),
  );

  await SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown],
  );

  await Workmanager().initialize(callbackDispatcher);
  runApp(app);
}

Future<App> buildApp(
  AppSettings appSettings,
  EmbarkKeyValueDatabase objectBox,
  Clock clock,
  BiLogger? logger,
) async {
  // prepare logger
  logger ??= GetIt.instance.get<BiLogger>();
  logger.setLogLevel(appSettings.logLevel);
  final remoteNotificationsService = getIt.get<RemoteNotificationsService>(
    instanceName: 'RemoteNotificationsService',
  );

  final notificationTapWhichLaunchedAppFromTerminated =
      await remoteNotificationsService
          .notificationTapWhichLaunchedAppFromTerminated();

  if (notificationTapWhichLaunchedAppFromTerminated != null) {
    await objectBox.pushNotificationsInfoBox.put(
      'notificationLaunchedTheApp',
      PushNotificationsInfo(
        notificationLaunchedApp:
            notificationTapWhichLaunchedAppFromTerminated.toJson(),
      ),
    );
  }

  return App();
}

@visibleForTesting
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(
  PushPayload notification,
) async {
  // android only implementation
  if (defaultTargetPlatform != TargetPlatform.android) return;

  if (!getIt.isRegistered<Starter>()) {
    getIt.registerSingleton<Starter>(
      await StarterImplementation.standard(),
    );
  }
  final starter = getIt.get<Starter>();
  if (!backgroundDependenciesInitialized) {
    await starter.initializeDependencyInjection(isForeground: false);
    backgroundDependenciesInitialized = true;
  }

  final remoteNotificationsService = getIt.get<RemoteNotificationsService>(
    instanceName: 'RemoteNotificationsService',
  );
  await remoteNotificationsService.initializeLocalNotifications();
  await remoteNotificationsService.showNotification(
    notification,
    isInAppNotification: false,
  );
}
