import 'dart:async';

import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_mdm_platform_api/models/_get_phone_number_result.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/_location_background_service_setup.dart';
import 'package:embark/_smartband_background_service_setup.dart';
import 'package:embark/api/_bi_dio_interceptor_configuration.dart';
import 'package:embark/api/_bi_embark_sts_http_client.dart';
import 'package:embark/api/_dio_device_type_switcher.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_bi_firebase.dart';
import 'package:embark/services/background_jobs/work_manager/jobs/smart_band_job.dart';
import 'package:embark/starter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:workmanager/workmanager.dart';

Future<void> prepareBackgroundJobDependencies({
  required FirebaseOptions firebaseOptions,
  required AppSettings appSettings,
}) async {
  // happens when the app is in the background. If the app is in the foreground,
  // this will also get hit but so will the backend event stream, so only raise
  // any alerts here.
  await BiFirebase.init(
    firebaseOptions,
    crashlyticsEnabled: true,
  );

  Starter? starter;

  if (getIt.isRegistered<Starter>()) {
    starter = getIt.get<Starter>();
  } else {
    getIt.registerSingleton<Starter>(
      starter = StarterImplementation(
        appSettings: appSettings,
      ),
    );

    await starter.initializeDependencyInjection(
      isForeground: false,
      // Setup the dependency injection for the background isolate, but override
      // the token manager so we have a unique token/credential store just for
      // background objects.
      overrides: DependencyOverrides(
        internetConnectivityRepositoryOverride: () =>
            BackgroundInternetConnectivity(),
        tokenManagerOverride: () => createBackgroundPartionManager(
          getIt.get(),
          getIt.get<BiEmbarkStsHttpClient>(),
          getIt.get(),
        ),
      ),
    );
    // Need to initialize datetime formatting so the API can understand dart values.
    // These go as a pair. Always initialize the DateTimeFormatter before DateTimes
    await initializeDateFormatting();
    CustomLocaleSetup.initializeDateTimes();
  }

  // Update the Dio configuration to be ready for any background isolate API requests.
  final deviceSetupRepository = getIt.get<DeviceSetupRepository>();
  BiDioInterceptorConfiguration.applyForBackgroundJob(
    dio: getIt.get(),
    logger: getIt.get(),
    tokenManager: getIt.get(),
    backgroundJobService: getIt.get(),
    deviceSetupRepository: deviceSetupRepository,
  );

  // This was needed because on mdm devices auth token was not available to background isolate
  final deviceSwitcher = getIt.get<DioDeviceTypeSwitcher>();
  if (deviceSetupRepository.lastKnownDeviceType == ResolvedDeviceType.mdm) {
    // Request Phone permission.
    // On mdm devices this should be non intrusive
    final phoneNumberResult =
        await deviceSetupRepository.getMdmPhoneNumberStatus();
    if (phoneNumberResult.status == GetPhoneNumberResultStatus.success &&
        phoneNumberResult.phoneNumber?.isNotEmpty == true) {
      deviceSwitcher.applyDeviceAuthentication(
        phoneNumber: phoneNumberResult.phoneNumber!,
      );
    }
  }
}

BiTokenManager createBackgroundPartionManager(
  FlutterSecureStorage secureStorage,
  BiAuthenticator authenticator,
  BiLogger logger,
) {
  return BiTokenManager(
    secureStorage,
    authenticator,
    logger,
    // use a different part of the secure storage that
    // way UI and background jobs dont' clash when storing
    // credentials or tokens.
    partition: 'background_jobs',
  );
}

@pragma('vm:entry-point')
Future<void> smartlinkBackgroundServiceEntrypoint(
  ServiceInstance instance,
) async {
  // happens when the app is in the background. If the app is in the foreground,
  // this will also get hit but so will the backend event stream, so only raise
  // any alerts here.
  WidgetsFlutterBinding.ensureInitialized();

  final (settings, options) =
      await (AppSettings.load(), AppSettings.loadFirebaseOptions()).wait;
  await prepareBackgroundJobDependencies(
    appSettings: settings,
    firebaseOptions: options,
  );

  final logger = getIt.get<BiLogger>();
  logger.addStandardProperties({'isolate': 'location/bluetooth'});
  logger.info(message: 'background service endpoint started');
  if (defaultTargetPlatform == TargetPlatform.android) {
    unawaited(beginTrackingOnBackgroundIsolate(instance));
    // Start the workmanager job to upload sensor data to the TA
    // TODO: change to TBD as per US25618
    await Workmanager().registerPeriodicTask(
      'upload_sensor_data',
      SmartBandJob.jobName,
    );
  }

  await prepareSmartBandBackendOnBackgroundIsolate(instance);
}

// Both platforms have various timeouts on starting background services.
class BackgroundInternetConnectivity implements InternetConnectivityRepository {
  @override
  Stream<List<ConnectivityResult>> connectionStream =
      Stream.value([ConnectivityResult.mobile]);

  @override
  FutureOr<void> close() {}

  @override
  bool get isClosed => false;

  @override
  Future<List<ConnectivityResult>> get reachability async =>
      [ConnectivityResult.mobile];

  @override
  List<ConnectivityResult>? get status => [ConnectivityResult.mobile];
}
